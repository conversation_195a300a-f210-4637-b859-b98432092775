import { WebSocketServer, WebSocket } from 'ws';
import { Server } from 'http';
import SystemMetricsService, { SystemMetrics } from './SystemMetricsService';
import { ThreatsCache } from './ThreatsCache';
import { MachineLearningCache } from './MachineLearningCache';

interface ThreatIncident {
  uid: string;
  [key: string]: unknown;
}

interface ThreatsDataStructure {
  incidents?: ThreatIncident[];
  [key: string]: unknown;
}

interface Deployment {
  data: { [timestamp: string]: number };
  name: string;
  namespace: string;
}

interface MachineLearningDataStructure {
  deployments?: Deployment[];
  [key: string]: unknown;
}

export interface WebSocketMessage {
  type:
    | 'system-metrics'
    | 'threats-update'
    | 'ml-update'
    | 'ping'
    | 'pong'
    | 'subscribe'
    | 'unsubscribe'
    | 'error';
  data?: unknown;
  timestamp: string;
  source?: 'cache' | 'api' | 'websocket';
}

export class UnifiedWebSocketService {
  private static instance: UnifiedWebSocketService;
  private wss: WebSocketServer | null = null;
  private metricsService: SystemMetricsService;
  private threatsCache: ThreatsCache | null = null;
  private mlCache: MachineLearningCache | null = null;
  private connections: Map<string, Set<WebSocket>> = new Map();
  private unsubscribeMetrics: (() => void) | null = null;

  // Track last broadcast timestamp to send only new incidents
  private lastBroadcastTimestamp: number = 0;

  private constructor() {
    this.metricsService = SystemMetricsService.getInstance();
    // Initialize connection sets
    this.connections.set('metrics', new Set());
    this.connections.set('threats', new Set());
    this.connections.set('ml', new Set());
  }

  public static getInstance(): UnifiedWebSocketService {
    if (!UnifiedWebSocketService.instance) {
      UnifiedWebSocketService.instance = new UnifiedWebSocketService();
    }
    return UnifiedWebSocketService.instance;
  }

  public setThreatsCache(threatsCache: ThreatsCache): void {
    this.threatsCache = threatsCache;
  }

  public setMachineLearningCache(mlCache: MachineLearningCache): void {
    this.mlCache = mlCache;
  }

  public initialize(server: Server): void {
    // Create a single WebSocket server with proper configuration
    this.wss = new WebSocketServer({
      server,
      perMessageDeflate: false, // Disable compression to avoid RSV1 issues
      maxPayload: 1024 * 1024, // 1MB max payload
      clientTracking: true,
      verifyClient: (info: { req: { url?: string } }) => {
        // Allow connections to /ws/metrics, /ws/threats, and /ws/ml
        const validPaths = ['/ws/metrics', '/ws/threats', '/ws/ml'];
        return validPaths.includes(info.req.url || '');
      },
    });

    this.wss.on('connection', (ws: WebSocket, request) => {
      const path = request.url || '';
      const remoteAddress = request.socket.remoteAddress;

      console.log(`New WebSocket connection from ${remoteAddress} to ${path}`);

      // Determine service type based on path
      let serviceType: 'metrics' | 'threats' | 'ml';
      if (path === '/ws/metrics') {
        serviceType = 'metrics';
      } else if (path === '/ws/threats') {
        serviceType = 'threats';
      } else if (path === '/ws/ml') {
        serviceType = 'ml';
      } else {
        console.warn(`Invalid WebSocket path: ${path}`);
        ws.close(1008, 'Invalid path');
        return;
      }

      // Add to appropriate connection set
      this.connections.get(serviceType)?.add(ws);

      // Send initial data based on service type
      setTimeout(() => {
        if (serviceType === 'metrics') {
          this.sendInitialMetrics(ws);
        }
        // Removed threats-initial sending - frontend now fetches via HTTP first
      }, 100);

      ws.on('message', (message: Buffer) => {
        try {
          // Validate message size
          if (message.length > 1024 * 10) {
            // 10KB limit
            console.warn('Received oversized message, ignoring');
            this.sendMessage(ws, {
              type: 'error',
              data: { error: 'Message too large' },
              timestamp: new Date().toISOString(),
            });
            return;
          }

          const data: WebSocketMessage = JSON.parse(message.toString('utf8'));

          // Validate message structure
          if (!data.type || typeof data.type !== 'string') {
            throw new Error(
              'Invalid message structure: missing or invalid type',
            );
          }

          this.handleMessage(ws, data, serviceType);
        } catch (error) {
          console.error(
            `Error parsing ${serviceType} WebSocket message:`,
            error,
          );
          this.sendMessage(ws, {
            type: 'error',
            data: { error: 'Invalid message format' },
            timestamp: new Date().toISOString(),
          });
        }
      });

      ws.on('close', () => {
        console.log(`${serviceType} WebSocket connection closed`);
        this.connections.get(serviceType)?.delete(ws);
        this.updateSubscriptions();
      });

      ws.on('error', (error) => {
        console.error(`${serviceType} WebSocket error:`, error);
        this.connections.get(serviceType)?.delete(ws);
        this.updateSubscriptions();
      });

      // Update subscriptions
      this.updateSubscriptions();
    });

    console.log(
      'Unified WebSocket server initialized for /ws/metrics and /ws/threats',
    );
  }

  private async sendInitialMetrics(ws: WebSocket): Promise<void> {
    try {
      const metrics = await this.metricsService.getCurrentMetrics();
      this.sendMessage(ws, {
        type: 'system-metrics',
        data: metrics,
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error('Error sending initial metrics:', error);
    }
  }

  private handleMessage(
    ws: WebSocket,
    message: WebSocketMessage,
    serviceType: 'metrics' | 'threats' | 'ml',
  ): void {
    switch (message.type) {
      case 'ping':
        this.sendMessage(ws, {
          type: 'pong',
          data: { timestamp: message.timestamp },
          timestamp: new Date().toISOString(),
        });
        break;

      case 'subscribe':
        // Client is already subscribed by connecting, send confirmation
        this.sendMessage(ws, {
          type: 'subscribe',
          data: { subscribed: true, service: serviceType },
          timestamp: new Date().toISOString(),
        });
        break;

      case 'unsubscribe':
        // For now, unsubscribe means close connection
        ws.close(1000, 'Unsubscribed');
        break;

      default:
        console.warn(
          `Unknown ${serviceType} WebSocket message type:`,
          message.type,
        );
    }
  }

  private sendMessage(ws: WebSocket, message: WebSocketMessage): void {
    if (ws.readyState !== WebSocket.OPEN) {
      return;
    }

    try {
      const messageString = JSON.stringify(message);
      // Validate message size to prevent frame issues
      if (messageString.length > 1024 * 1024) {
        // 1MB limit
        console.error('Message too large, truncating data');
        const truncatedMessage = {
          ...message,
          data: { error: 'Data too large, please use REST API' },
        };
        ws.send(JSON.stringify(truncatedMessage));
        return;
      }

      ws.send(messageString);
    } catch (error) {
      console.error('Error sending WebSocket message:', error);
      // Remove the problematic connection
      this.connections.get('metrics')?.delete(ws);
      this.connections.get('threats')?.delete(ws);
      try {
        ws.close(1011, 'Message send error');
      } catch (closeError) {
        console.error('Error closing problematic WebSocket:', closeError);
      }
    }
  }

  private broadcastMetrics(metrics: SystemMetrics): void {
    const message: WebSocketMessage = {
      type: 'system-metrics',
      data: metrics,
      timestamp: new Date().toISOString(),
    };

    const metricsConnections = this.connections.get('metrics');
    if (metricsConnections) {
      metricsConnections.forEach((ws) => {
        this.sendMessage(ws, message);
      });
    }
  }

  public broadcastThreats(
    threatsData: unknown,
    source: 'cache' | 'api' | 'websocket' = 'cache',
  ): void {
    const threatsConnections = this.connections.get('threats');
    if (!threatsConnections || threatsConnections.size === 0) {
      return; // No clients connected
    }

    const data = threatsData as ThreatsDataStructure;
    if (!data || !data.incidents || !Array.isArray(data.incidents)) {
      console.warn(
        'UnifiedWebSocketService: Invalid threats data structure for broadcast',
      );
      return;
    }

    // Filter only new incidents (newer than last broadcast)
    /* const newIncidents = data.incidents.filter(
      (incident) => incident && typeof incident.timestamp === 'number',
      //typeof incident.timestamp === 'number' &&
      //incident.timestamp > this.lastBroadcastTimestamp,
    ); */
    const newIncidents = data.incidents;

    if (newIncidents.length === 0) {
      console.log('UnifiedWebSocketService: No new incidents to broadcast');
      return;
    }

    // Update last broadcast timestamp
    const maxTimestamp = Math.max(
      ...newIncidents.map((i) => i.timestamp as number),
      this.lastBroadcastTimestamp,
    );
    this.lastBroadcastTimestamp = maxTimestamp;

    console.log(
      `UnifiedWebSocketService: Broadcasting ${newIncidents.length} new incidents to ${threatsConnections.size} clients`,
    );

    // Send only new incidents as threats-update
    const updateMessage: WebSocketMessage = {
      type: 'threats-update',
      data: {
        incidents: newIncidents,
        action: 'append', // Clear action indicating these should be appended
      },
      source,
      timestamp: new Date().toISOString(),
    };

    threatsConnections.forEach((ws) => {
      this.sendMessage(ws, updateMessage);
    });
  }

  public broadcastMachineLearning(
    mlData: unknown,
    source: 'cache' | 'api' | 'websocket' = 'cache',
  ): void {
    const mlConnections = this.connections.get('ml');
    if (!mlConnections || mlConnections.size === 0) {
      return; // No clients connected
    }

    const data = mlData as MachineLearningDataStructure;
    if (!data || !data.deployments || !Array.isArray(data.deployments)) {
      console.warn(
        'UnifiedWebSocketService: Invalid ML data structure for broadcast',
      );
      return;
    }

    const deployments = data.deployments;

    console.log(
      `UnifiedWebSocketService: Broadcasting ${deployments.length} deployments to ${mlConnections.size} clients (including empty arrays for deletion updates)`,
    );

    // Send deployments as ml-update
    let action: 'append' | 'update' | 'clear';

    if (source === 'cache' && deployments.length === 0) {
      // Cache source with empty deployments = deletion/clear
      action = 'clear';
    } else if (source === 'cache' && deployments.length > 0) {
      // Cache source with deployments = full update (after deletion)
      action = 'update';
    } else {
      // WebSocket source or API source = append (original behavior)
      action = 'append';
    }

    const mlUpdateData = {
      deployments: deployments,
      action: action,
    };

    const updateMessage: WebSocketMessage = {
      type: 'ml-update',
      data: mlUpdateData,
      source,
      timestamp: new Date().toISOString(),
    };

    console.log(
      `UnifiedWebSocketService: Sending ML update - action: ${mlUpdateData.action}, deployments: ${deployments.length}, source: ${source}`,
    );

    mlConnections.forEach((ws) => {
      this.sendMessage(ws, updateMessage);
    });
  }

  private updateSubscriptions(): void {
    const metricsConnections = this.connections.get('metrics')?.size || 0;
    const hasMetricsConnections = metricsConnections > 0;

    if (hasMetricsConnections && !this.unsubscribeMetrics) {
      // Start monitoring
      this.unsubscribeMetrics = this.metricsService.subscribe((metrics) => {
        this.broadcastMetrics(metrics);
      });
      this.metricsService.startMonitoring(2000); // Update every 2 seconds
    } else if (!hasMetricsConnections && this.unsubscribeMetrics) {
      // Stop monitoring
      this.unsubscribeMetrics();
      this.unsubscribeMetrics = null;
      this.metricsService.stopMonitoring();
    }
  }

  public getConnectionCount(service?: 'metrics' | 'threats' | 'ml'): number {
    if (service) {
      return this.connections.get(service)?.size || 0;
    }
    // Return total connections
    return (
      (this.connections.get('metrics')?.size || 0) +
      (this.connections.get('threats')?.size || 0)
    );
  }

  public close(): void {
    if (this.unsubscribeMetrics) {
      this.unsubscribeMetrics();
      this.unsubscribeMetrics = null;
    }

    this.metricsService.stopMonitoring();

    // Reset tracking
    this.lastBroadcastTimestamp = 0;

    // Close all connections
    this.connections.forEach((connectionSet) => {
      connectionSet.forEach((ws) => {
        ws.close(1000, 'Server shutting down');
      });
      connectionSet.clear();
    });

    if (this.wss) {
      this.wss.close();
      this.wss = null;
    }

    console.log('Unified WebSocket service closed');
  }
}

export default UnifiedWebSocketService;

#!/bin/bash

# Start Next.js development server with proper port handling

# Load environment variables from .env.local if it exists
if [ -f ".env.local" ]; then
    echo "📋 Loading environment from .env.local..."
    set -a
    source .env.local
    set +a
fi

# Get port from environment variable or default to 3000
PORT=${NEXT_PORT:-3000}

echo "🚀 Starting development server on port $PORT..."

# Start Next.js dev server
next dev -p "$PORT"

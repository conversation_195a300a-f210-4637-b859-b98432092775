// Logo asset paths and metadata
export const LOGO_ASSETS = {
  svg: {
    full: '/logos/telesoft-full.svg',
    icon: '/logos/telesoft-icon.svg',
    text: '/logos/telesoft-text.svg',
  },
  png: {
    full: {
      small: '/logos/telesoft-full-24.png',
      medium: '/logos/telesoft-full-32.png',
      large: '/logos/telesoft-full-48.png',
      xlarge: '/logos/telesoft-full-64.png',
    },
    icon: {
      small: '/logos/telesoft-icon-24.png',
      medium: '/logos/telesoft-icon-32.png',
      large: '/logos/telesoft-icon-48.png',
      xlarge: '/logos/telesoft-icon-64.png',
    },
    'full-logo-white-blue': '/logos/full-logo-white-blue.png',
    'full-logo': '/logos/full-logo.png',
  },
} as const;


export const COMPONENT_IMAGES = {
  windows: '/images/Windows.png',
  linux: '/images/Linux.png',
  mpac6650: '/images/MPAC6650.png',
} as const;

export type LogoAssetType = keyof typeof LOGO_ASSETS;
export type ComponentImageType = keyof typeof COMPONENT_IMAGES;
export type LogoVariant =
  | 'full'
  | 'icon'
  | 'text'
  | 'full-logo-white-blue'
  | 'full-logo';
export type LogoSize = 'small' | 'medium' | 'large' | 'xlarge';

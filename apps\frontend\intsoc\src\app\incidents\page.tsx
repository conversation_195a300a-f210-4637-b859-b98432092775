'use client';

import {
  Card,
  CardContent,
  Alert,
  AlertTitle,
  AlertDescription,
  Button,
} from '@telesoft/ui';
import { ThreatIncident } from '@telesoft/types';
import { useThreatFilters, useThreatStats } from '../../lib/hooks/useThreats';
import { useThreatsContext } from '../../lib/contexts/ThreatsContext';
import { useState, useMemo, useEffect } from 'react';
import Modal from '../../components/Modal';
import {
  getMitreAttackTechniques,
  getTacticColor,
} from '../../lib/mitre-attack';

const THREAT_TYPE_LABELS: Record<ThreatIncident['incident_type'], string> = {
  beaconing: 'Beaconing',
  spike: 'Traffic Spike',
  ddos: 'DDoS Attack',
  outlier: 'Anomaly Detection',
  dga: 'Domain Generation Algorithm',
  anomaly: 'Anomaly Detection',
};

function formatTimestamp(timestamp: number) {
  // Check if timestamp is in seconds or milliseconds
  // If timestamp is less than 10^10, it's likely in seconds and needs conversion
  const date =
    timestamp < 10000000000 ? new Date(timestamp * 1000) : new Date(timestamp);

  // Format as DD/MM/YYYY HH:MM:SS (24-hour clock)
  const day = date.getDate().toString().padStart(2, '0');
  const month = (date.getMonth() + 1).toString().padStart(2, '0');
  const year = date.getFullYear();
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');

  return `${day}/${month}/${year} ${hours}:${minutes}:${seconds}`;
}

function ThreatStats({ stats }: { stats: ReturnType<typeof useThreatStats> }) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
      <Card className="glass-effect border-border-primary elevation-low">
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-text-primary">
            {stats.total}
          </div>
          <div className="text-sm text-text-secondary">Incidents</div>
        </CardContent>
      </Card>

      <Card className="glass-effect border-border-primary elevation-low">
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-text-danger">
            {stats.critical}
          </div>
          <div className="text-sm text-text-secondary">Critical</div>
        </CardContent>
      </Card>

      <Card className="glass-effect border-border-primary elevation-low">
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-text-amber">{stats.high}</div>
          <div className="text-sm text-text-secondary">High</div>
        </CardContent>
      </Card>

      <Card className="glass-effect border-border-primary elevation-low">
        <CardContent className="p-4 text-center">
          <div className="text-2xl font-bold text-text-danger">
            {stats.newIncidents}
          </div>
          <div className="text-sm text-text-secondary">New Incidents</div>
        </CardContent>
      </Card>
    </div>
  );
}

function ThreatFilters({
  filters,
  setFilters,
  resetFilters,
}: {
  filters: any;
  setFilters: (filters: any) => void;
  resetFilters: () => void;
}) {
  return (
    <Card className="glass-effect border-border-primary elevation-low mb-6">
      <CardContent className="p-4">
        <div className="flex flex-col md:flex-row md:items-end gap-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 flex-1">
            <div>
              <label className="block text-xs font-medium text-text-primary mb-1">
                Detection Type
              </label>
              <select
                value={filters.type || ''}
                onChange={(e) =>
                  setFilters({ type: e.target.value || undefined })
                }
                className="w-full px-3 py-1.5 text-sm bg-surface-secondary border border-border-muted rounded-md text-text-primary"
              >
                <option value="">All Types</option>
                {Object.entries(THREAT_TYPE_LABELS).map(([value, label]) => (
                  <option key={value} value={value}>
                    {label}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-xs font-medium text-text-primary mb-1">
                Severity Level
              </label>
              <select
                value={filters.level || ''}
                onChange={(e) =>
                  setFilters({ level: e.target.value || undefined })
                }
                className="w-full px-3 py-1.5 text-sm bg-surface-secondary border border-border-muted rounded-md text-text-primary"
              >
                <option value="">All Levels</option>
                <option value="critical">Critical</option>
                <option value="high">High</option>
                <option value="medium">Medium</option>
                <option value="low">Low</option>
                <option value="info">Info</option>
              </select>
            </div>

            <div>
              <label className="block text-xs font-medium text-text-primary mb-1">
                Status
              </label>
              <select
                value={filters.progress || ''}
                onChange={(e) =>
                  setFilters({ progress: e.target.value || undefined })
                }
                className="w-full px-3 py-1.5 text-sm bg-surface-secondary border border-border-muted rounded-md text-text-primary"
              >
                <option value="">All Statuses</option>
                <option value="created">Created</option>
                <option value="running">Running</option>
                <option value="complete">Complete</option>
                <option value="failed">Failed</option>
              </select>
            </div>
          </div>

          <div className="flex-shrink-0">
            <Button onClick={resetFilters} variant="outline" size="sm">
              Reset
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default function ThreatsPage() {
  const { threats, isConnecting, error, refetch } = useThreatsContext();
  const { filteredThreats, filters, setFilters, resetFilters } =
    useThreatFilters(threats);
  const stats = useThreatStats(threats);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const threatsPerPage = 10;

  // Modal state for threat details
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedThreat, setSelectedThreat] = useState<ThreatIncident | null>(
    null,
  );
  const [activeTab, setActiveTab] = useState<
    'summary' | 'risk-analysis' | 'remediation' | 'mitre-attack'
  >('summary');

  // Sort and paginate threats
  const sortedThreats = useMemo(() => {
    return filteredThreats.sort((a, b) => b.time - a.time);
  }, [filteredThreats]);

  const totalPages = Math.ceil(sortedThreats.length / threatsPerPage);
  const startIndex = (currentPage - 1) * threatsPerPage;
  const paginatedThreats = sortedThreats.slice(
    startIndex,
    startIndex + threatsPerPage,
  );

  // Reset to page 1 when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [filteredThreats.length]);

  // Handle viewing threat details
  const handleViewDetail = (threat: ThreatIncident) => {
    setSelectedThreat(threat);
    setActiveTab('summary'); // Reset to summary tab
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedThreat(null);
  };

  // Handle silence functionality (placeholder for now)
  const handleSilence = (threat: ThreatIncident) => {
    // TODO: Implement silence functionality
    console.log('Silencing threat:', threat.uid);
    // For now, just show the modal like view detail
    handleViewDetail(threat);
  };

  // Map WebSocket states to the expected loading state
  const loading = isConnecting && threats.length === 0;

  if (loading) {
    return (
      <div className="min-h-screen bg-background-primary p-8 pt-24">
        <div className="mx-auto max-w-7xl">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-accent-primary mx-auto mb-4"></div>
              <p className="text-text-secondary">Loading threats data...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background-primary p-8 pt-24">
        <div className="mx-auto max-w-7xl">
          <Alert className="mb-6">
            <AlertTitle>Error Loading Threats</AlertTitle>
            <AlertDescription>
              {error}
              <div className="mt-4">
                <Button onClick={refetch} variant="outline">
                  Try Again
                </Button>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background-primary p-8 pt-24">
      <div className="mx-auto max-w-7xl">
        {/* Statistics */}
        <ThreatStats stats={stats} />

        {/* Filters */}
        <ThreatFilters
          filters={filters}
          setFilters={setFilters}
          resetFilters={resetFilters}
        />

        {/* Threats Table */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-xl font-semibold text-text-primary">
              Incidents ({filteredThreats.length})
            </h2>
            <div className="text-sm text-text-secondary">
              Page {currentPage} of {totalPages}
            </div>
          </div>

          {filteredThreats.length === 0 ? (
            <Card className="glass-effect border-border-primary elevation-low">
              <CardContent className="p-8 text-center">
                <p className="text-text-secondary">
                  No threats match the current filters.
                </p>
              </CardContent>
            </Card>
          ) : (
            <>
              <Card className="glass-effect border-border-primary elevation-low">
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    <table className="w-full">
                      <thead>
                        <tr className="border-b border-border-muted">
                          <th className="text-left p-4 text-sm font-semibold text-text-primary">
                            Time
                          </th>
                          <th className="text-left p-4 text-sm font-semibold text-text-primary">
                            Detection Type
                          </th>
                          <th className="text-left p-4 text-sm font-semibold text-text-primary">
                            Description
                          </th>
                          <th className="text-left p-4 text-sm font-semibold text-text-primary">
                            Severity
                          </th>
                          <th className="text-left p-4 text-sm font-semibold text-text-primary">
                            Status
                          </th>
                        </tr>
                      </thead>
                      <tbody>
                        {paginatedThreats.map((threat) => (
                          <tr
                            key={threat.uid}
                            className="border-b border-border-muted hover:bg-surface-secondary/50 transition-colors"
                          >
                            <td className="p-4">
                              <span className="text-sm text-text-primary">
                                {formatTimestamp(threat.time)}
                              </span>
                            </td>
                            <td className="p-4">
                              <span className="text-sm font-medium text-text-primary">
                                {THREAT_TYPE_LABELS[threat.incident_type]}
                              </span>
                            </td>
                            <td className="p-4">
                              <span className="text-sm text-text-secondary max-w-xs truncate block">
                                {threat.incident_description || 'No description available'}
                              </span>
                            </td>
                            <td className="p-4">
                              <span
                                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${threat.risk_severity === 'critical'
                                  ? 'bg-cyber-danger-500/20 text-cyber-danger-400 border border-cyber-danger-500/50'
                                  : threat.risk_severity === 'high'
                                    ? 'bg-cyber-amber-500/20 text-cyber-amber-400 border border-cyber-amber-500/50'
                                    : threat.risk_severity === 'medium'
                                      ? 'bg-cyber-warning-500/20 text-cyber-warning-400 border border-cyber-warning-500/50'
                                      : threat.risk_severity === 'low'
                                        ? 'bg-green-500/20 text-green-400 border border-green-500/50'
                                        : threat.risk_severity === 'info'
                                          ? 'bg-blue-500/20 text-blue-400 border border-blue-500/50'
                                          : threat.risk_severity === 'unknown'
                                            ? 'bg-gray-900/20 text-gray-900 border border-gray-900/50'
                                            : 'bg-cyber-matrix-500/20 text-cyber-matrix-400 border border-cyber-matrix-500/50'
                                  }`}
                              >
                                {threat.risk_severity.charAt(0).toUpperCase() +
                                  threat.risk_severity.slice(1)}
                              </span>
                            </td>
                            <td className="p-4">
                              <span
                                className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${threat.investigation_status === 'created'
                                  ? 'bg-green-500/20 text-green-400 border border-green-500/50'
                                  : threat.investigation_status === 'running'
                                    ? 'bg-blue-500/20 text-blue-400 border border-blue-500/50'
                                    : threat.investigation_status ===
                                      'complete'
                                      ? 'bg-emerald-500/20 text-emerald-400 border border-emerald-500/50'
                                      : threat.investigation_status ===
                                        'failed'
                                        ? 'bg-red-500/20 text-red-400 border border-red-500/50'
                                        : 'bg-gray-500/20 text-gray-400 border border-gray-500/50'
                                  }`}
                              >
                                {threat.investigation_status
                                  .charAt(0)
                                  .toUpperCase() +
                                  threat.investigation_status.slice(1)}
                              </span>
                            </td>
                            <td className="p-4">
                              <div className="flex space-x-2">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  className="whitespace-nowrap text-xs"
                                  onClick={() => handleViewDetail(threat)}
                                >
                                  View Detail
                                </Button>
                              </div>
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between">
                  <div className="text-sm text-text-secondary">
                    Showing {startIndex + 1} to{' '}
                    {Math.min(
                      startIndex + threatsPerPage,
                      sortedThreats.length,
                    )}{' '}
                    of {sortedThreats.length} threats
                  </div>
                  <div className="flex items-center space-x-2">
                    {/* Go to first page */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(1)}
                      disabled={currentPage === 1}
                      className="w-10 h-10 p-0"
                      title="Go to first page"
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="11,17 6,12 11,7"></polyline>
                        <polyline points="18,17 13,12 18,7"></polyline>
                      </svg>
                    </Button>

                    {/* Previous page */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage((prev) => Math.max(1, prev - 1))
                      }
                      disabled={currentPage === 1}
                      className="w-10 h-10 p-0"
                      title="Previous page"
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="15,18 9,12 15,6"></polyline>
                      </svg>
                    </Button>

                    {/* Page numbers */}
                    <div className="flex items-center space-x-1">
                      {Array.from(
                        { length: Math.min(5, totalPages) },
                        (_, i) => {
                          let pageNum;
                          if (totalPages <= 5) {
                            pageNum = i + 1;
                          } else {
                            // Show current page and 2 pages on each side when possible
                            const start = Math.max(
                              1,
                              Math.min(currentPage - 2, totalPages - 4),
                            );
                            pageNum = start + i;
                          }

                          return (
                            <Button
                              key={pageNum}
                              variant={
                                currentPage === pageNum ? 'primary' : 'ghost'
                              }
                              size="sm"
                              onClick={() => setCurrentPage(pageNum)}
                              className="w-10 h-10 p-0"
                            >
                              {pageNum}
                            </Button>
                          );
                        },
                      )}
                    </div>

                    {/* Next page */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() =>
                        setCurrentPage((prev) => Math.min(totalPages, prev + 1))
                      }
                      disabled={currentPage === totalPages}
                      className="w-10 h-10 p-0"
                      title="Next page"
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="9,18 15,12 9,6"></polyline>
                      </svg>
                    </Button>

                    {/* Go to last page */}
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setCurrentPage(totalPages)}
                      disabled={currentPage === totalPages}
                      className="w-10 h-10 p-0"
                      title="Go to last page"
                    >
                      <svg
                        width="24"
                        height="24"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="13,17 18,12 13,7"></polyline>
                        <polyline points="6,17 11,12 6,7"></polyline>
                      </svg>
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </div>

        {/* Threat Detail Modal */}
        {isModalOpen && selectedThreat && (
          <Modal
            isOpen={isModalOpen}
            onClose={handleCloseModal}
            type={selectedThreat.incident_type}
            severity={selectedThreat.risk_severity}
            time={formatTimestamp(selectedThreat.time)}
            tabs={[
              {
                id: 'summary',
                label: 'Summary',
                active: activeTab === 'summary',
                onClick: () => setActiveTab('summary'),
              },
              {
                id: 'risk-analysis',
                label: 'Risk Analysis',
                active: activeTab === 'risk-analysis',
                onClick: () => setActiveTab('risk-analysis'),
              },
              {
                id: 'remediation',
                label: 'Remediation Advice',
                active: activeTab === 'remediation',
                onClick: () => setActiveTab('remediation'),
              },
              {
                id: 'mitre-attack',
                label: 'Mitre ATT&CK',
                active: activeTab === 'mitre-attack',
                onClick: () => setActiveTab('mitre-attack'),
              },
            ]}
          >
            <div className="min-h-[200px] p-6">
              {activeTab === 'summary' && (
                <div>
                  {selectedThreat.summary ? (
                    <div className="text-text-secondary whitespace-pre-wrap leading-relaxed">
                      {selectedThreat.summary}
                    </div>
                  ) : (
                    <div className="text-text-secondary italic">
                      No summary available for this threat.
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'risk-analysis' && (
                <div>
                  {selectedThreat.risk_message ? (
                    <div className="text-text-secondary whitespace-pre-wrap leading-relaxed">
                      {selectedThreat.risk_message}
                    </div>
                  ) : (
                    <div className="text-text-secondary italic">
                      No risk analysis available for this threat.
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'remediation' && (
                <div>
                  {selectedThreat.remediation_actions ? (
                    <div className="text-text-secondary whitespace-pre-wrap leading-relaxed">
                      {selectedThreat.remediation_actions}
                    </div>
                  ) : (
                    <div className="text-text-secondary italic">
                      No remediation advice available for this threat.
                    </div>
                  )}
                </div>
              )}

              {activeTab === 'mitre-attack' && (
                <div className="space-y-6">
                  {/* Current TTPs Section */}
                  <div>
                    <h3 className="text-base font-semibold text-text-primary mb-4 flex items-center gap-2">
                      <span className="w-3 h-3 bg-cyber-danger-500 rounded-full"></span>
                      Current TTPs Detected
                    </h3>
                    {selectedThreat.current_ttps &&
                      selectedThreat.current_ttps.length > 0 ? (
                      <div className="space-y-3">
                        {getMitreAttackTechniques(
                          selectedThreat.current_ttps,
                        ).map((ttp, index) => (
                          <div
                            key={index}
                            className="bg-surface-secondary/30 border border-border-primary/30 rounded-lg p-4"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-3">
                                <span className="font-mono text-sm bg-cyber-danger-500/20 text-cyber-danger-400 px-2 py-1 rounded border border-cyber-danger-500/30">
                                  {ttp.id}
                                </span>
                                <span
                                  className={`font-semibold ${ttp.found ? 'text-text-primary' : 'text-text-secondary'}`}
                                >
                                  {ttp.name}
                                </span>
                              </div>
                              {!ttp.found && (
                                <span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded border border-yellow-500/30">
                                  Unknown
                                </span>
                              )}
                            </div>
                            <div className="mb-2">
                              <span
                                className={`inline-block text-xs px-2 py-1 rounded border ${getTacticColor(ttp.tactic)}`}
                              >
                                {ttp.tactic}
                              </span>
                            </div>
                            {ttp.description && (
                              <p className="text-sm text-text-secondary leading-relaxed">
                                {ttp.description}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-text-secondary italic">
                        No current TTPs detected for this threat.
                      </div>
                    )}
                  </div>

                  {/* Future TTPs Section */}
                  <div>
                    <h3 className="text-base font-semibold text-text-primary mb-4 flex items-center gap-2">
                      <span className="w-3 h-3 bg-cyber-warning-500 rounded-full"></span>
                      Potential Future TTPs
                    </h3>
                    {selectedThreat.future_ttps &&
                      selectedThreat.future_ttps.length > 0 ? (
                      <div className="space-y-3">
                        {getMitreAttackTechniques(
                          selectedThreat.future_ttps,
                        ).map((ttp, index) => (
                          <div
                            key={index}
                            className="bg-surface-secondary/20 border border-border-primary/20 rounded-lg p-4"
                          >
                            <div className="flex items-start justify-between mb-2">
                              <div className="flex items-center gap-3">
                                <span className="font-mono text-sm bg-cyber-warning-500/20 text-cyber-warning-400 px-2 py-1 rounded border border-cyber-warning-500/30">
                                  {ttp.id}
                                </span>
                                <span
                                  className={`font-semibold ${ttp.found ? 'text-text-primary' : 'text-text-secondary'}`}
                                >
                                  {ttp.name}
                                </span>
                              </div>
                              {!ttp.found && (
                                <span className="text-xs bg-yellow-500/20 text-yellow-400 px-2 py-1 rounded border border-yellow-500/30">
                                  Unknown
                                </span>
                              )}
                            </div>
                            <div className="mb-2">
                              <span
                                className={`inline-block text-xs px-2 py-1 rounded border ${getTacticColor(ttp.tactic)}`}
                              >
                                {ttp.tactic}
                              </span>
                            </div>
                            {ttp.description && (
                              <p className="text-sm text-text-secondary leading-relaxed">
                                {ttp.description}
                              </p>
                            )}
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-text-secondary italic">
                        No future TTPs predicted for this threat.
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>
          </Modal>
        )}
      </div>
    </div>
  );
}

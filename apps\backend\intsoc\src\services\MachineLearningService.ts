import {
  WebSocketConnectionManager,
  ConnectionConfig,
} from './WebSocketConnectionManager';
import { MessageParser, ParsedMessage } from '../utils/MessageParser';
import { MachineLearningCache } from './MachineLearningCache';
import UnifiedWebSocketService from './UnifiedWebSocketService';
import config from '../config';
import { randomUUID } from 'crypto';
import fetch from 'node-fetch';
import { URL } from 'url';
 
export interface MachineLearningServiceConfig {
  mlCache: MachineLearningCache;
  cacheTimeout?: number;
  connectionConfig?: Partial<ConnectionConfig>;
}
 
export interface MachineLearningData {
  deployments?: unknown[];
  [key: string]: unknown;
}
 
export interface DeploymentItem {
  data: { [timestamp: string]: number };
  name: string;
  namespace: string;
}
 
export interface CreateDeploymentRequest {
  type: 'spike' | 'dga';
  name: string;
  sourceField?: string;
  filterType?: string;
  filterValue?: string;
  COUNT_TYPE?: string;
}
 
export interface CreateDeploymentResponse {
  success: boolean;
  data?: unknown;
  error?: string;
  namespace?: string;
}
 
export interface DeleteDeploymentItem {
  type: 'spike' | 'dga';
  namespace: string;
}
 
export interface DeleteDeploymentRequest {
  deployments: DeleteDeploymentItem[];
}
 
export interface DeleteDeploymentResponse {
  success: boolean;
  deletedCount?: number;
  errors?: string[];
  message?: string;
}
 
/**
 * Machine Learning service using the connection manager
 *
 * Handles WebSocket messages from ML service:
 * - First message contains complete dataset and is stored directly
 * - Subsequent messages contain incremental updates and are merged with existing data
 * - Objects are uniquely identified by combination of name and namespace
 * - Data fields (timestamps and values) are merged for matching deployments
 */
export class MachineLearningService {
  private static readonly CONNECTION_ID = 'ml-websocket';
  private static instance: MachineLearningService | null = null;
 
  private connectionManager: WebSocketConnectionManager;
  private mlCache: MachineLearningCache;
  private cacheTimeout: number;
  private isInitialized = false;
  private hasReceivedInitialData = false;
 
  constructor(serviceConfig: MachineLearningServiceConfig) {
    // Warn if multiple instances are created
    if (MachineLearningService.instance !== null) {
      console.warn(
        'MachineLearningService: Multiple instances detected. Consider using singleton pattern.',
      );
    }
 
    this.connectionManager = WebSocketConnectionManager.getInstance();
    this.mlCache = serviceConfig.mlCache;
    this.cacheTimeout = serviceConfig.cacheTimeout || 3600;
 
    this.setupEventHandlers();
    MachineLearningService.instance = this;
  }
 
  /**
   * Get the singleton instance or create a new one
   */
  public static getInstance(
    serviceConfig?: MachineLearningServiceConfig,
  ): MachineLearningService {
    if (!MachineLearningService.instance && serviceConfig) {
      MachineLearningService.instance = new MachineLearningService(
        serviceConfig,
      );
    } else if (!MachineLearningService.instance) {
      throw new Error(
        'MachineLearningService: Must provide serviceConfig for first initialization',
      );
    }
    return MachineLearningService.instance;
  }
 
  /**
   * Initialize the machine learning WebSocket connection
   */
  public async initialize(): Promise<void> {
    if (this.isInitialized) {
      console.log('MachineLearningService: Already initialized');
      return;
    }
 
    const connectionConfig: ConnectionConfig = {
      url: config.externalApi.mlWebSocketUrl,
      reconnectDelayMs: 5000,
      maxReconnectAttempts: -1,
      connectionTimeoutMs: 10000,
      enableAutoReconnect: true,
      enableLogging: true,
    };
 
    try {
      await this.connectionManager.createConnection(
        MachineLearningService.CONNECTION_ID,
        connectionConfig,
      );
 
      // Check if we have existing cached data to determine initial data state
      const hasExistingData = await this.mlCache.hasValidMachineLearningData();
      if (hasExistingData) {
        this.hasReceivedInitialData = true;
        console.log(
          'MachineLearningService: Found existing cached data, treating subsequent messages as updates',
        );
      }
 
      this.isInitialized = true;
      console.log('MachineLearningService: Successfully initialized');
    } catch (error) {
      console.error('MachineLearningService: Failed to initialize:', error);
      throw error;
    }
  }
 
  /**
   * Stop the machine learning WebSocket connection
   */
  public disconnect(): void {
    if (!this.isInitialized) return;
 
    this.connectionManager.disconnect(MachineLearningService.CONNECTION_ID);
    console.log('MachineLearningService: Disconnected');
  }
 
  /**
   * Get the current WebSocket connection status
   */
  public getConnectionStatus(): string {
    if (!this.isInitialized) return 'not-initialized';
    return this.connectionManager.getConnectionStatus(
      MachineLearningService.CONNECTION_ID,
    );
  }
 
  /**
   * Check if WebSocket is currently connected
   */
  public isConnected(): boolean {
    if (!this.isInitialized) return false;
    return this.connectionManager.isConnected(
      MachineLearningService.CONNECTION_ID,
    );
  }
 
  /**
   * Get connection statistics
   */
  public getStats() {
    if (!this.isInitialized) return null;
    return this.connectionManager.getStats(
      MachineLearningService.CONNECTION_ID,
    );
  }
 
  /**
   * Send data through the WebSocket connection
   */
  public send(data: string | Buffer): boolean {
    if (!this.isInitialized) {
      console.warn(
        'MachineLearningService: Cannot send data - not initialized',
      );
      return false;
    }
    return this.connectionManager.send(
      MachineLearningService.CONNECTION_ID,
      data,
    );
  }
 
  /**
   * Set the cache timeout for machine learning data
   */
  public setCacheTimeout(timeoutSeconds: number): void {
    this.cacheTimeout = timeoutSeconds;
  }
 
  /**
   * Get the current cache timeout
   */
  public getCacheTimeout(): number {
    return this.cacheTimeout;
  }
 
  /**
   * Test connectivity to the external ML API
   * @returns Promise<boolean> - True if the API is reachable, false otherwise
   */
  public async testConnectivity(): Promise<{ success: boolean; message: string; url: string }> {
    try {
      const baseUrl = config.externalApi.mlApiUrl;
      console.log(`MachineLearningService: Testing connectivity to ${baseUrl}`);
 
      // Try a simple GET request to the base URL or health endpoint
      const response = await fetch(baseUrl, {
        method: 'GET',
        headers: { 'User-Agent': 'intsoc-backend/1.0' },
      });
 
      return {
        success: true,
        message: `Successfully connected to ML API (HTTP ${response.status})`,
        url: baseUrl
      };
    } catch (error: any) {
      const errorMessage = error?.code === 'ECONNRESET'
        ? 'Connection reset by server'
        : error?.code === 'ECONNREFUSED'
          ? 'Connection refused - service may be down'
          : error?.message || 'Unknown error';
 
      return {
        success: false,
        message: `Failed to connect to ML API: ${errorMessage}`,
        url: config.externalApi.mlApiUrl
      };
    }
  }
 
  /**
   * Create a new deployment via external API (POST, JSON body)
   * Updated to match the new API contract with improved structure
   *
   * @param request - The deployment request containing type ('spike' or 'dga') and name (display name)
   * @returns Promise<CreateDeploymentResponse> - Response with success status, data, and generated namespace
   */
  public async createDeployment(
    request: CreateDeploymentRequest,
  ): Promise<CreateDeploymentResponse> {
    try {
      // Generate a unique namespace (or use the name as suggested)
      const namespace = request.name.toLowerCase().replace(/[^a-z0-9-]/g, '-');
 

      const payload = {
        chart: "spike-detection-charts", // Optional chart field as mentioned
        name: request.type, // 'spike' or 'dga' - derived from type as suggested
        namespace: namespace, // Use deployment name as namespace
        values: {
          env: {
            consumer: {
              COUNT_TYPE: request.COUNT_TYPE || '',
              FILTER_KEY: request.sourceField || '',
              FILTER_TYPE: request.filterType || '',
              FILTER_VALUE: request.filterValue || '',
            },
          },
        },
      };
 
      // Construct the URL properly - ensure it ends with /create
      const baseUrl = config.externalApi.mlApiUrl.endsWith('/')
        ? config.externalApi.mlApiUrl
        : config.externalApi.mlApiUrl + '/';
      const url = new URL('create', baseUrl);
 
      // Use POST method as requested - external API team needs to implement this
      const usePostMethod = true; // Using POST as per requirements
 
      let response;
      try {
        if (usePostMethod) {
          // Use POST with JSON payload (new API contract)
          console.log(
            `MachineLearningService: Creating deployment with POST to: ${url.toString()}`,
            'Payload:',
            JSON.stringify(payload, null, 2),
          );
 
          response = await fetch(url.toString(), {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'intsoc-backend/1.0',
              'Accept': 'application/json'
            },
            body: JSON.stringify(payload),
          });
        } else {
          // Use GET with query parameters (current API contract)
          url.searchParams.append('name', payload.name);
          url.searchParams.append('namespace', payload.namespace);
          url.searchParams.append('display_name', request.name);
          url.searchParams.append('sourceField', payload.values.env.consumer.FILTER_KEY);
          url.searchParams.append('filterType', payload.values.env.consumer.FILTER_TYPE);
          url.searchParams.append('filterValue', payload.values.env.consumer.FILTER_VALUE);
 
          console.log(
            `MachineLearningService: Creating deployment with GET to: ${url.toString()}`,
            'Note: Using GET with query parameters (set ML_API_USE_POST=true to use POST method)'
          );
 
          response = await fetch(url.toString(), {
            method: 'GET',
            headers: {
              'User-Agent': 'intsoc-backend/1.0',
              'Accept': 'application/json'
            },
          });
        }
      } catch (fetchError: any) {
        console.error(`MachineLearningService: Network error connecting to ${url.toString()}:`, fetchError);
 
        // Provide more specific error messages based on error type
        if (fetchError?.code === 'ECONNRESET') {
          throw new Error(`Connection reset by external ML API server at ${url.toString()}. The server may be down or rejecting connections.`);
        } else if (fetchError?.code === 'ECONNREFUSED') {
          throw new Error(`Connection refused by external ML API server at ${url.toString()}. Check if the service is running.`);
        } else if (fetchError?.code === 'ETIMEDOUT') {
          throw new Error(`Timeout connecting to external ML API server at ${url.toString()}. The server may be slow or unreachable.`);
        } else {
          throw new Error(`Network error connecting to external ML API: ${fetchError?.message || 'Unknown network error'}`);
        }
      }
 
      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unable to read error response');
        console.error(`MachineLearningService: External API error (${response.status}):`, errorText);
        throw new Error(`External API returned status: ${response.status} - ${errorText}`);
      }
 
      const responseData = await response.json();
 
      const deployment = {
        namespace,
        type: request.type,
        name: request.name,
        sourceField: request.sourceField || '',
        filterType: request.filterType || '',
        filterValue: request.filterValue || '',
      };
 
      console.log('MachineLearningService: Deployment created successfully', deployment);
 
      return {
        success: true,
        data: responseData,
        ...deployment,
      };
    } catch (error) {
      console.error(
        'MachineLearningService: Failed to create deployment:',
        error,
      );
 
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
    }
  }
 
  /**
   * Delete multiple deployments
   * @param request - DeleteDeploymentRequest containing array of deployments to delete
   * @returns Promise<DeleteDeploymentResponse> - Response with success status and deletion results
   *
   * Example usage:
   * const result = await mlService.deleteDeployments({
   *   deployments: [
   *     { type: 'spike', name: 'deployment1' },
   *     { type: 'dga', name: 'deployment2' }
   *   ]
   * });
   */
  public async deleteDeployments(
    request: DeleteDeploymentRequest,
  ): Promise<DeleteDeploymentResponse> {
    try {
      const errors: string[] = [];
      let deletedCount = 0;
      const successfullyDeleted: Array<{ name: string; namespace: string }> =
        [];
 
      // Process each deployment deletion
      for (const deployment of request.deployments) {
        try {
          // Build the URL for the delete endpoint
          const baseUrl = config.externalApi.mlApiUrl.endsWith('/')
            ? config.externalApi.mlApiUrl
            : config.externalApi.mlApiUrl + '/';
          const url = new URL('delete', baseUrl);

          // Create payload for DELETE request - use the deployment type as name
          const payload = {
            name: deployment.type,
            namespace: deployment.namespace
          };


          // Make the DELETE request to the external API for deletion
          const response = await fetch(url.toString(), {
            method: 'DELETE',
            headers: {
              'Content-Type': 'application/json',
              'User-Agent': 'intsoc-backend/1.0',
              'Accept': 'application/json'
            },
            body: JSON.stringify(payload),
          });

          if (!response.ok) {
            const errorText = await response.text().catch(() => 'No error details');
            const errorMsg = `Failed to delete deployment ${deployment.namespace} (${deployment.type}): HTTP ${response.status} - ${errorText}`;
            errors.push(errorMsg);
            console.error('MachineLearningService:', errorMsg);
          } else {
            deletedCount++;
            successfullyDeleted.push({
              name: deployment.type,
              namespace: deployment.namespace,
            });
            console.log(
              'MachineLearningService: Deployment deleted successfully',
              { type: deployment.type, namespace: deployment.namespace },
            );
          }
        } catch (error) {
          const errorMsg = `Error deleting deployment ${deployment.namespace} (${deployment.type}): ${error instanceof Error ? error.message : 'Unknown error'}`;
          errors.push(errorMsg);
          console.error('MachineLearningService:', errorMsg);
        }
      }
 
      // Update cache to remove successfully deleted deployments
      if (successfullyDeleted.length > 0) {
        try {
          const cacheUpdated =
            await this.mlCache.removeDeployments(successfullyDeleted);
          if (cacheUpdated) {
            console.log(
              `MachineLearningService: Cache updated - removed ${successfullyDeleted.length} deployment(s)`,
            );
 
            // Broadcast the updated data to connected clients
            try {
              const updatedData = await this.mlCache.getMachineLearningData();
              if (updatedData) {
                const unifiedWsService = UnifiedWebSocketService.getInstance();
                // Broadcast the updated data (including empty arrays after deletion)
                console.log(
                  'MachineLearningService: Broadcasting deployment deletions to connected clients',
                );
                unifiedWsService.broadcastMachineLearning(updatedData, 'cache');
              }
            } catch (broadcastError) {
              console.warn(
                'MachineLearningService: Failed to broadcast cache update:',
                broadcastError,
              );
            }
          } else {
            console.warn(
              'MachineLearningService: Failed to update cache after deletions',
            );
          }
        } catch (cacheError) {
          console.error(
            'MachineLearningService: Error updating cache after deletions:',
            cacheError,
          );
        }
      }
 
      const success = deletedCount > 0;
      const hasErrors = errors.length > 0;
 
      return {
        success,
        deletedCount,
        errors: hasErrors ? errors : undefined,
        message: success
          ? `Successfully deleted ${deletedCount} deployment(s)${hasErrors ? `, with ${errors.length} error(s)` : ''}`
          : `Failed to delete any deployments. ${errors.length} error(s) occurred.`,
      };
    } catch (error) {
      console.error(
        'MachineLearningService: Failed to delete deployments:',
        error,
      );
 
      return {
        success: false,
        deletedCount: 0,
        errors: [error instanceof Error ? error.message : 'Unknown error'],
        message: 'Failed to delete deployments due to unexpected error',
      };
    }
  }
 
  /**
   * Cleanup resources
   */
  public cleanup(): void {
    if (this.isInitialized) {
      this.connectionManager.removeConnection(
        MachineLearningService.CONNECTION_ID,
      );
      this.isInitialized = false;
    }
    // Reset flags
    this.hasReceivedInitialData = false;
    // Reset singleton instance
    MachineLearningService.instance = null;
    console.log('MachineLearningService: Cleaned up');
  }
 
  /**
   * Set up event handlers for WebSocket events
   */
  private setupEventHandlers(): void {
    this.connectionManager.on('connected', (connectionId: string) => {
      if (connectionId === MachineLearningService.CONNECTION_ID) {
        console.log('MachineLearningService: Connected to ML WebSocket API');
      }
    });
 
    this.connectionManager.on(
      'disconnected',
      (connectionId: string, code: number, reason?: string) => {
        if (connectionId === MachineLearningService.CONNECTION_ID) {
          console.log(
            `MachineLearningService: Disconnected (code: ${code}, reason: ${reason || 'none'})`,
          );
        }
      },
    );
 
    this.connectionManager.on(
      'message',
      async (connectionId: string, data: Buffer | string) => {
        if (connectionId === MachineLearningService.CONNECTION_ID) {
          await this.handleMachineLearningMessage(data);
        }
      },
    );
 
    this.connectionManager.on('error', (connectionId: string, error: Error) => {
      if (connectionId === MachineLearningService.CONNECTION_ID) {
        console.error(
          'MachineLearningService: WebSocket error:',
          error.message,
        );
      }
    });
 
    this.connectionManager.on(
      'reconnecting',
      (connectionId: string, attempt: number) => {
        if (connectionId === MachineLearningService.CONNECTION_ID) {
          console.log(
            `MachineLearningService: Reconnecting (attempt ${attempt})...`,
          );
        }
      },
    );
 
    this.connectionManager.on('reconnectFailed', (connectionId: string) => {
      if (connectionId === MachineLearningService.CONNECTION_ID) {
        console.error(
          'MachineLearningService: Failed to reconnect after maximum attempts',
        );
      }
    });
  }
 
  /**
   * Handle incoming machine learning data from WebSocket
   */
  private async handleMachineLearningMessage(
    data: Buffer | string,
  ): Promise<void> {
    try {
      // Convert data to string and parse
      const messageString = MessageParser.dataToString(data);
 
      let parseResult: ParsedMessage = MessageParser.parseJson(messageString);
 
      // If parsing failed and message contains single quotes, try manual fix
      if (!parseResult.success && messageString.includes("'")) {
        console.log(
          'ML WebSocket: Standard parsing failed, trying manual quote fix',
        );
        try {
          // Manual conversion of single quotes to double quotes for this specific format
          const fixedMessage = messageString.replace(/'/g, '"');
          console.log('ML WebSocket fixed message:', fixedMessage);
 
          const manualData = JSON.parse(fixedMessage);
          parseResult = {
            success: true,
            data: manualData,
            raw: messageString,
          };
          console.log('ML WebSocket: Manual parsing succeeded');
        } catch (manualError) {
          console.warn(
            'ML WebSocket: Manual parsing also failed:',
            manualError,
          );
        }
      }
 
      if (!parseResult.success) {
        console.warn(
          'MachineLearningService: Failed to parse message:',
          parseResult.error,
        );
        return;
      }
 
      let mlData: MachineLearningData;
 
      if (Array.isArray(parseResult.data)) {
        // Data is a direct array of deployments - wrap it
        mlData = { deployments: parseResult.data };
        console.log('ML data is direct array, wrapping in deployments object');
      } else if (this.isValidMachineLearningData(parseResult.data)) {
        // Data is already in expected format
        mlData = parseResult.data as MachineLearningData;
        console.log('ML data is already in expected format');
      } else {
        console.warn('MachineLearningService: Received invalid ML data format');
        console.log(
          'Expected array or object with deployments, got:',
          typeof parseResult.data,
        );
        return;
      }
 
      const deployments = mlData.deployments;
      const deploymentsCount = Array.isArray(deployments)
        ? deployments.length
        : 0;
 
      console.log(
        `MachineLearningService: Received valid ML data: ${deploymentsCount} items`,
      );
 
      let stored: boolean;
 
      // Determine if this is initial data or an update
      if (!this.hasReceivedInitialData) {
        // First message contains complete dataset - store directly
        console.log('MachineLearningService: Storing initial complete dataset');
        stored = await this.mlCache.storeMachineLearningData(
          mlData,
          this.cacheTimeout,
        );
        if (stored) {
          this.hasReceivedInitialData = true;
          console.log(
            'MachineLearningService: Initial dataset stored successfully',
          );
        }
      } else {
        // Subsequent messages are incremental updates - merge with existing data
        console.log('MachineLearningService: Merging incremental update');
        stored = await this.mlCache.mergeMachineLearningData(
          mlData,
          this.cacheTimeout,
        );
        if (stored) {
          console.log(
            'MachineLearningService: Incremental update merged successfully',
          );
        }
      }
 
      if (stored) {
        console.log(
          'MachineLearningService: Successfully appended ML data to cache',
        );
 
        // Only broadcast after successful cache write to ensure data consistency
        const unifiedWsService = UnifiedWebSocketService.getInstance();
        unifiedWsService.broadcastMachineLearning(mlData, 'websocket');
      } else {
        console.warn(
          'MachineLearningService: Failed to append ML data to cache',
        );
      }
    } catch (error) {
      console.error(
        'MachineLearningService: Error handling ML message:',
        error,
      );
    }
  }
 
  /**
   * Validate machine learning data structure
   */
  private isValidMachineLearningData(
    data: unknown,
  ): data is MachineLearningData {
    return data !== null && typeof data === 'object';
  }
 
  /**
   * Reset the initial data flag - useful for testing or when cache is cleared
   */
  public resetInitialDataFlag(): void {
    this.hasReceivedInitialData = false;
    console.log('MachineLearningService: Reset initial data flag');
  }
 
  /**
   * Check if initial data has been received
   */
  public hasReceivedInitialMachineLearningData(): boolean {
    return this.hasReceivedInitialData;
  }
}

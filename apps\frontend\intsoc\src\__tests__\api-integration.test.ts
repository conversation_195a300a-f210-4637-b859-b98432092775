/**
 * Integration test for threats API communication
 * Tests the complete flow from frontend to backend API
 */

import { threatsService } from '../lib/services/threats';
import { apiClient } from '../lib/api-client';
import { ThreatIncident } from '@telesoft/types';

// Mock fetch for testing
global.fetch = jest.fn();

describe('API Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('ThreatsService', () => {
    it('should fetch threats from API successfully', async () => {
      const mockResponse = {
        incidents: [
          {
            incident_type: 'beaconing',
            risk_severity: 'medium',
            investigation_status: 'running',
            investigation_outcome: 'automation triggered',
            time: 1749565938038,
            uid: 'test-uid-1',
          },
          {
            incident_type: 'ddos',
            risk_severity: 'critical',
            investigation_status: 'created',
            investigation_outcome: 'escalated',
            time: 1749565938039,
            uid: 'test-uid-2',
          },
        ],
      };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse,
      });

      const threats = await threatsService.getThreats();

      expect(fetch).toHaveBeenCalledWith(
        'http://localhost:4001/api/v1/threats',
        expect.objectContaining({
          method: 'GET',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        }),
      );

      expect(threats).toHaveLength(2);
      expect(threats[0].incident_type).toBe('beaconing');
      expect(threats[1].risk_severity).toBe('critical');
    });

    it('should handle API errors gracefully', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: false,
        status: 500,
        statusText: 'Internal Server Error',
      });

      await expect(threatsService.getThreats()).rejects.toThrow(
        'HTTP 500: Internal Server Error',
      );
    });

    it('should filter threats by type correctly', () => {
      const threats: ThreatIncident[] = [
        {
          incident_type: 'beaconing',
          risk_severity: 'low',
          investigation_status: 'created',
          time: 123,
          uid: '1',
        },
        {
          incident_type: 'ddos',
          risk_severity: 'high',
          investigation_status: 'created',
          time: 124,
          uid: '2',
        },
        {
          incident_type: 'beaconing',
          risk_severity: 'medium',
          investigation_status: 'complete',
          time: 125,
          uid: '3',
        },
      ];

      const beaconingThreats = threatsService.filterByType(
        threats,
        'beaconing',
      );
      expect(beaconingThreats).toHaveLength(2);
      expect(
        beaconingThreats.every((t) => t.incident_type === 'beaconing'),
      ).toBe(true);
    });

    it('should get critical threats correctly', () => {
      const threats: ThreatIncident[] = [
        {
          incident_type: 'beaconing',
          risk_severity: 'critical',
          investigation_status: 'created',
          investigation_outcome: 'closed',
          time: 123,
          uid: '1',
        },
        {
          incident_type: 'ddos',
          risk_severity: 'critical',
          investigation_status: 'complete',
          investigation_outcome: 'action taken',
          time: 124,
          uid: '2',
        },
        {
          incident_type: 'spike',
          risk_severity: 'critical',
          investigation_status: 'running',
          investigation_outcome: 'automation triggered',
          time: 125,
          uid: '3',
        },
        {
          incident_type: 'outlier',
          risk_severity: 'medium',
          investigation_status: 'created',
          investigation_outcome: 'escalated',
          time: 126,
          uid: '4',
        },
      ];

      const criticalThreats = threatsService.getCriticalThreats(threats);
      expect(criticalThreats).toHaveLength(2);
      expect(criticalThreats.every((t) => t.risk_severity === 'critical')).toBe(
        true,
      );
      expect(
        criticalThreats.every((t) => t.investigation_status !== 'complete'),
      ).toBe(true);
    });
  });

  describe('ApiClient', () => {
    it('should handle timeout errors', async () => {
      const mockAbortError = new DOMException(
        'The operation was aborted',
        'AbortError',
      );
      (fetch as jest.Mock).mockRejectedValueOnce(mockAbortError);

      await expect(apiClient.get('/test')).rejects.toThrow('Request timeout');
    });

    it('should include proper headers', async () => {
      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ data: 'test' }),
      });

      await apiClient.get('/test');

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
        }),
      );
    });

    it('should handle POST requests with data', async () => {
      const testData = { name: 'test' };

      (fetch as jest.Mock).mockResolvedValueOnce({
        ok: true,
        json: async () => ({ success: true }),
      });

      await apiClient.post('/test', testData);

      expect(fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: 'POST',
          body: JSON.stringify(testData),
        }),
      );
    });
  });
});
